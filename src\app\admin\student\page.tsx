// import AdminLayout from "@/components/admin/AdminLayout";

// export interface Student {
//   id: string;
//   prn: string;
//   username: string;
//   email: string;
//   mentorID?: string | null;
//   createdAt?: string;
// }

// export default function StudentManagementPage() {
//   return (
//     <>
//       <AdminLayout
//         pageTitle="Student Management"
//         pageDescription="Manage student enrollments and access"
//       >
        
//       </AdminLayout>
//     </>
//   );
// }
