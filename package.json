{"name": "finding-finance-association", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mailchimp/mailchimp_marketing": "^3.0.80", "classnames": "^2.5.1", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "framer-motion": "^12.15.0", "lucide-react": "^0.515.0", "next": "^15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/classnames": "^2.3.0", "@types/mailchimp__mailchimp_marketing": "^3.0.21", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}